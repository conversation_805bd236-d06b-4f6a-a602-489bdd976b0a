# AgenticAI Component

This directory contains the AgenticAI component, which is responsible for executing AI agents with tools and memory.

## MCP Tool Configuration

The AgenticAI component now supports the correct formatting of MCP tools for the agent platform. The `get_agent_config` method separates MCP tools from regular tools and formats them according to the agent platform's requirements.

### Implementation Details

The transformation process involves:

1. Separating MCP tools from regular tools based on the presence of `mcp_metadata`
2. Transforming each MCP tool to the format expected by the agent platform
3. Grouping MCP tools by server ID to create a single MCP config for each server
4. Creating MCP configs with the required structure, including `mcp_tools_config`

### MCP Config Format

The MCP config format expected by the agent platform is:

```json
{
  "mcp_tools_config": {
    "meta": null,
    "nextCursor": null,
    "tools": [
      {
        "name": "tool_name",
        "description": "Tool description",
        "input_schema": { ... },
        "output_schema": { ... },
        "annotations": null
      }
    ]
  },
  "is_added": false,
  "env_keys": null,
  "component_category": "social media",
  "env_credential_status": null
}
```

### Usage

The AgenticAI component automatically handles the transformation of MCP tools. When you connect MCP marketplace components to the AgenticAI component's tools handle, they will be properly formatted in the agent configuration.

## Testing

The component includes tests to verify the correct formatting of MCP tools:

- `test_agent_config_format_with_mcp_tools`: Tests that MCP tools are correctly separated and formatted
- `test_agent_config_format_with_no_mcp_tools`: Tests that the component works correctly with no MCP tools
- `test_agent_config_format_with_multiple_mcp_servers`: Tests that the component correctly handles multiple MCP servers
