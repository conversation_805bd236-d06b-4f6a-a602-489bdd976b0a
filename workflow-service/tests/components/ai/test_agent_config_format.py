"""
Tests for the AgenticAI component's agent configuration format.
"""

import pytest
from unittest.mock import Mock
from app.components.ai.agentic_ai import AgenticAI
from app.models.workflow_builder.context import WorkflowContext


def test_agent_config_format_with_mcp_tools():
    """Test that the agent config format correctly separates MCP tools."""
    # Create AgenticAI component
    component = AgenticAI()
    
    # Create mock context with MCP tools
    context = Mock(spec=WorkflowContext)
    context.current_node_id = "agentic-ai-1"
    
    # Mock tool connections
    tool_connections = [
        {
            "tool_type": "workflow_component",
            "component": {
                "component_id": "regular-tool",
                "component_type": "DataProcessor",
                "component_name": "Regular Tool",
                "component_description": "A regular workflow component"
            }
        },
        {
            "tool_type": "workflow_component",
            "component": {
                "component_id": "mcp-tool-1",
                "component_type": "MCPMarketplace",
                "component_name": "MCP Tool 1",
                "component_description": "An MCP marketplace component",
                "input_schema": {
                    "properties": {
                        "input": {"type": "string"}
                    },
                    "required": ["input"]
                },
                "output_schema": {
                    "properties": {
                        "output": {"type": "string"}
                    }
                },
                "mcp_metadata": {
                    "server_id": "server-1",
                    "tool_name": "tool-1"
                }
            }
        },
        {
            "tool_type": "workflow_component",
            "component": {
                "component_id": "mcp-tool-2",
                "component_type": "MCPMarketplace",
                "component_name": "MCP Tool 2",
                "component_description": "Another MCP marketplace component",
                "input_schema": {
                    "properties": {
                        "input": {"type": "string"}
                    },
                    "required": ["input"]
                },
                "output_schema": {
                    "properties": {
                        "output": {"type": "string"}
                    }
                },
                "mcp_metadata": {
                    "server_id": "server-1",
                    "tool_name": "tool-2"
                }
            }
        }
    ]
    
    # Mock _extract_connected_workflow_components to return our tool connections
    component._extract_connected_workflow_components = Mock(return_value=tool_connections)
    
    # Mock get_input_value to return default values
    def mock_get_input_value(name, ctx, default=None):
        values = {
            "description": "Test agent",
            "execution_type": "response",
            "query": "Test query",
            "system_message": "Test system message",
            "termination_condition": "",
            "max_tokens": 1000,
            "model_provider": "OpenAI",
            "model_name": "gpt-4",
            "temperature": 0.7,
            "name": "Test Agent",
            "id": "agentic-ai-1"
        }
        return values.get(name, default)
    
    component.get_input_value = mock_get_input_value
    
    # Get agent config
    agent_config = component.get_agent_config(context)
    
    # Verify structure
    assert "tools" in agent_config
    assert "mcps" in agent_config
    
    # Verify regular tools
    assert len(agent_config["tools"]) == 1
    assert agent_config["tools"][0]["component"]["component_id"] == "regular-tool"
    
    # Verify MCP configs
    assert len(agent_config["mcps"]) == 1
    
    # Verify MCP config structure
    mcp_config = agent_config["mcps"][0]
    assert "mcp_tools_config" in mcp_config
    assert "tools" in mcp_config["mcp_tools_config"]
    assert len(mcp_config["mcp_tools_config"]["tools"]) == 2
    assert mcp_config["mcp_tools_config"]["tools"][0]["name"] == "tool-1"
    assert mcp_config["mcp_tools_config"]["tools"][1]["name"] == "tool-2"
    assert "is_added" in mcp_config
    assert "component_category" in mcp_config


def test_agent_config_format_with_no_mcp_tools():
    """Test that the agent config format works correctly with no MCP tools."""
    # Create AgenticAI component
    component = AgenticAI()
    
    # Create mock context with regular tools only
    context = Mock(spec=WorkflowContext)
    context.current_node_id = "agentic-ai-1"
    
    # Mock tool connections
    tool_connections = [
        {
            "tool_type": "workflow_component",
            "component": {
                "component_id": "regular-tool-1",
                "component_type": "DataProcessor",
                "component_name": "Regular Tool 1",
                "component_description": "A regular workflow component"
            }
        },
        {
            "tool_type": "workflow_component",
            "component": {
                "component_id": "regular-tool-2",
                "component_type": "TextAnalyzer",
                "component_name": "Regular Tool 2",
                "component_description": "Another regular workflow component"
            }
        }
    ]
    
    # Mock _extract_connected_workflow_components to return our tool connections
    component._extract_connected_workflow_components = Mock(return_value=tool_connections)
    
    # Mock get_input_value to return default values
    def mock_get_input_value(name, ctx, default=None):
        values = {
            "description": "Test agent",
            "execution_type": "response",
            "query": "Test query",
            "system_message": "Test system message",
            "termination_condition": "",
            "max_tokens": 1000,
            "model_provider": "OpenAI",
            "model_name": "gpt-4",
            "temperature": 0.7,
            "name": "Test Agent",
            "id": "agentic-ai-1"
        }
        return values.get(name, default)
    
    component.get_input_value = mock_get_input_value
    
    # Get agent config
    agent_config = component.get_agent_config(context)
    
    # Verify structure
    assert "tools" in agent_config
    assert "mcps" in agent_config
    
    # Verify regular tools
    assert len(agent_config["tools"]) == 2
    assert agent_config["tools"][0]["component"]["component_id"] == "regular-tool-1"
    assert agent_config["tools"][1]["component"]["component_id"] == "regular-tool-2"
    
    # Verify MCP configs (should be empty)
    assert len(agent_config["mcps"]) == 0


def test_agent_config_format_with_multiple_mcp_servers():
    """Test that the agent config format correctly handles multiple MCP servers."""
    # Create AgenticAI component
    component = AgenticAI()
    
    # Create mock context with MCP tools from multiple servers
    context = Mock(spec=WorkflowContext)
    context.current_node_id = "agentic-ai-1"
    
    # Mock tool connections
    tool_connections = [
        {
            "tool_type": "workflow_component",
            "component": {
                "component_id": "mcp-tool-1",
                "component_type": "MCPMarketplace",
                "component_name": "MCP Tool 1",
                "component_description": "An MCP marketplace component",
                "mcp_metadata": {
                    "server_id": "server-1",
                    "tool_name": "tool-1"
                }
            }
        },
        {
            "tool_type": "workflow_component",
            "component": {
                "component_id": "mcp-tool-2",
                "component_type": "MCPMarketplace",
                "component_name": "MCP Tool 2",
                "component_description": "Another MCP marketplace component",
                "mcp_metadata": {
                    "server_id": "server-1",
                    "tool_name": "tool-2"
                }
            }
        },
        {
            "tool_type": "workflow_component",
            "component": {
                "component_id": "mcp-tool-3",
                "component_type": "MCPMarketplace",
                "component_name": "MCP Tool 3",
                "component_description": "An MCP marketplace component from another server",
                "mcp_metadata": {
                    "server_id": "server-2",
                    "tool_name": "tool-3"
                }
            }
        }
    ]
    
    # Mock _extract_connected_workflow_components to return our tool connections
    component._extract_connected_workflow_components = Mock(return_value=tool_connections)
    
    # Mock get_input_value to return default values
    def mock_get_input_value(name, ctx, default=None):
        values = {
            "description": "Test agent",
            "execution_type": "response",
            "query": "Test query",
            "system_message": "Test system message",
            "termination_condition": "",
            "max_tokens": 1000,
            "model_provider": "OpenAI",
            "model_name": "gpt-4",
            "temperature": 0.7,
            "name": "Test Agent",
            "id": "agentic-ai-1"
        }
        return values.get(name, default)
    
    component.get_input_value = mock_get_input_value
    
    # Get agent config
    agent_config = component.get_agent_config(context)
    
    # Verify structure
    assert "tools" in agent_config
    assert "mcps" in agent_config
    
    # Verify regular tools (should be empty)
    assert len(agent_config["tools"]) == 0
    
    # Verify MCP configs
    assert len(agent_config["mcps"]) == 2
    
    # Create a map of server IDs to MCP configs
    server_to_config = {}
    for config in agent_config["mcps"]:
        for tool in config["mcp_tools_config"]["tools"]:
            server_id = "server-1" if tool["name"] in ["tool-1", "tool-2"] else "server-2"
            if server_id not in server_to_config:
                server_to_config[server_id] = config
    
    # Verify server-1 config
    assert "server-1" in server_to_config
    server1_config = server_to_config["server-1"]
    assert len(server1_config["mcp_tools_config"]["tools"]) == 2
    tool_names = [tool["name"] for tool in server1_config["mcp_tools_config"]["tools"]]
    assert "tool-1" in tool_names
    assert "tool-2" in tool_names
    
    # Verify server-2 config
    assert "server-2" in server_to_config
    server2_config = server_to_config["server-2"]
    assert len(server2_config["mcp_tools_config"]["tools"]) == 1
    assert server2_config["mcp_tools_config"]["tools"][0]["name"] == "tool-3"
