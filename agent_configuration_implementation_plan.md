# Agent Configuration Transformation Implementation Plan

## Overview

This document outlines a comprehensive implementation plan to address the mismatch between how MCP tools are formatted across the RUH AI backend components. The plan follows a Test-Driven Development (TDD) approach to ensure high code quality and prevent regressions.

## Problem Statement

There is a mismatch in how MCP tools are handled across three components:
1. The workflow service (AgenticAI component) includes MCP servers in the `tools` field
2. The agent platform expects MCP servers in the `mcps` field with a specific format that includes `mcp_tools_config`
3. The orchestration engine needs to adapt the configuration between these two formats

## Implementation Approach

We will follow a TDD approach with these phases:
1. Analysis and verification of current behavior
2. Test creation for new functionality
3. Implementation of the adaptation logic
4. Verification and integration testing

## Phase 1: Analysis and Verification

### 1.1 Analyze Current Configuration Flow

```python
# Create test script to analyze current configuration flow
def analyze_configuration_flow():
    # 1. Create a sample agent configuration with MCP tools
    sample_config = create_sample_agent_config_with_mcp_tools()
    
    # 2. Log the configuration structure at each step
    print("Workflow Service Output:")
    print(json.dumps(sample_config, indent=2))
    
    # 3. Simulate orchestration engine processing
    orchestration_output = simulate_orchestration_processing(sample_config)
    print("Orchestration Engine Output:")
    print(json.dumps(orchestration_output, indent=2))
    
    # 4. Compare with expected agent platform format
    expected_format = create_expected_agent_platform_format()
    print("Expected Agent Platform Format:")
    print(json.dumps(expected_format, indent=2))
    
    # 5. Identify discrepancies
    identify_discrepancies(orchestration_output, expected_format)
```

### 1.2 Verify Current Behavior

Create unit tests to verify the current behavior of the system:

```python
def test_current_workflow_service_output():
    """Test that the workflow service produces the expected output format."""
    component = AgenticAI()
    context = create_test_context_with_mcp_tools()
    
    # Get agent config
    agent_config = component.get_agent_config(context)
    
    # Verify structure
    assert "tools" in agent_config
    assert len(agent_config["tools"]) > 0
    
    # Verify MCP tool structure
    mcp_tools = [t for t in agent_config["tools"] 
                if t.get("tool_type") == "workflow_component" and 
                   t.get("component", {}).get("mcp_metadata")]
    
    assert len(mcp_tools) > 0
    
    # Verify tool structure
    for tool in mcp_tools:
        assert "component" in tool
        assert "mcp_metadata" in tool["component"]
        assert "tool_name" in tool["component"]["mcp_metadata"]
        assert "server_id" in tool["component"]["mcp_metadata"]

def test_current_orchestration_engine_processing():
    """Test that the orchestration engine correctly processes agent configurations."""
    # Create sample agent config
    agent_config = create_sample_agent_config_with_mcp_tools()
    
    # Create agent executor instance
    executor = AgentExecutor()
    
    # Process configuration
    processed_config = executor._build_component_agent_request(
        agent_config, "test query", "response", "req-123", {}
    )
    
    # Verify structure
    assert "tools" in processed_config
    assert "agent_config" in processed_config
    assert "system_message" in processed_config
    assert "model_config" in processed_config
```

## Phase 2: Test Creation

### 2.1 Create Tests for Adaptation Logic

```python
def test_mcp_tool_transformation():
    """Test that MCP tools are correctly transformed to the required format."""
    # Create sample MCP tool
    mcp_tool = {
        "tool_type": "workflow_component",
        "component": {
            "component_id": "mcp-tool-1",
            "component_type": "MCPTool",
            "component_name": "MCP Tool",
            "component_description": "Test MCP tool",
            "input_schema": {
                "properties": {
                    "input": {"type": "string"}
                },
                "required": ["input"]
            },
            "output_schema": {
                "properties": {
                    "output": {"type": "string"}
                }
            },
            "mcp_metadata": {
                "server_id": "server-1",
                "tool_name": "test_tool"
            }
        }
    }
    
    # Transform tool
    transformer = MCPToolTransformer()
    transformed = transformer.transform_mcp_tool(mcp_tool)
    
    # Verify transformation
    assert "name" in transformed
    assert transformed["name"] == "test_tool"
    assert "description" in transformed
    assert "input_schema" in transformed
    assert "output_schema" in transformed
    assert "annotations" in transformed
    assert transformed["annotations"] is None

def test_mcp_tools_grouping_by_server():
    """Test that MCP tools are correctly grouped by server ID."""
    # Create sample MCP tools from same server
    mcp_tools = [
        create_mcp_tool("server-1", "tool-1", "Tool 1"),
        create_mcp_tool("server-1", "tool-2", "Tool 2"),
        create_mcp_tool("server-2", "tool-3", "Tool 3")
    ]
    
    # Group tools
    transformer = MCPToolTransformer()
    grouped = transformer.group_tools_by_server(mcp_tools)
    
    # Verify grouping
    assert "server-1" in grouped
    assert "server-2" in grouped
    assert len(grouped["server-1"]) == 2
    assert len(grouped["server-2"]) == 1
    assert grouped["server-1"][0]["name"] == "tool-1"
    assert grouped["server-1"][1]["name"] == "tool-2"
    assert grouped["server-2"][0]["name"] == "tool-3"

def test_mcp_config_creation():
    """Test that MCP configs are correctly created from grouped tools."""
    # Create grouped tools
    grouped_tools = {
        "server-1": [
            {"name": "tool-1", "description": "Tool 1", "input_schema": {}, "output_schema": {}, "annotations": None},
            {"name": "tool-2", "description": "Tool 2", "input_schema": {}, "output_schema": {}, "annotations": None}
        ]
    }
    
    # Create MCP configs
    transformer = MCPToolTransformer()
    configs = transformer.create_mcp_configs(grouped_tools)
    
    # Verify configs
    assert len(configs) == 1
    config = configs[0]
    assert "mcp_tools_config" in config
    assert "tools" in config["mcp_tools_config"]
    assert len(config["mcp_tools_config"]["tools"]) == 2
    assert "is_added" in config
    assert "env_keys" in config
    assert "component_category" in config
    assert "env_credential_status" in config
```

### 2.2 Create Integration Tests

```python
def test_orchestration_engine_adaptation():
    """Test that the orchestration engine correctly adapts MCP tools."""
    # Create sample agent config with MCP tools
    agent_config = create_sample_agent_config_with_mcp_tools()
    
    # Create agent executor instance with adaptation logic
    executor = AgentExecutor()
    
    # Process configuration
    processed_config = executor._build_component_agent_request(
        agent_config, "test query", "response", "req-123", {}
    )
    
    # Verify structure
    assert "tools" in processed_config
    assert "mcps" in processed_config
    
    # Verify regular tools are preserved
    regular_tools = [t for t in agent_config["tools"] 
                    if not (t.get("tool_type") == "workflow_component" and 
                           t.get("component", {}).get("mcp_metadata"))]
    assert len(processed_config["tools"]) == len(regular_tools)
    
    # Verify MCP tools are transformed
    mcp_tools = [t for t in agent_config["tools"] 
                if t.get("tool_type") == "workflow_component" and 
                   t.get("component", {}).get("mcp_metadata")]
    
    # Count unique server IDs
    server_ids = set()
    for tool in mcp_tools:
        server_id = tool.get("component", {}).get("mcp_metadata", {}).get("server_id")
        if server_id:
            server_ids.add(server_id)
    
    # Verify number of MCP configs matches number of unique servers
    assert len(processed_config["mcps"]) == len(server_ids)
    
    # Verify MCP config structure
    for mcp_config in processed_config["mcps"]:
        assert "mcp_tools_config" in mcp_config
        assert "tools" in mcp_config["mcp_tools_config"]
        assert len(mcp_config["mcp_tools_config"]["tools"]) > 0
        assert "is_added" in mcp_config
        assert "component_category" in mcp_config
```

## Phase 3: Implementation

### 3.1 Create MCP Tool Transformer

```python
class MCPToolTransformer:
    """
    Transforms MCP tools from workflow service format to agent platform format.
    """
    
    def transform_mcp_tool(self, mcp_tool: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transform a single MCP tool to the required format.
        
        Args:
            mcp_tool: MCP tool in workflow service format
            
        Returns:
            Transformed MCP tool in agent platform format
        """
        component = mcp_tool.get("component", {})
        mcp_metadata = component.get("mcp_metadata", {})
        
        return {
            "name": mcp_metadata.get("tool_name", ""),
            "description": component.get("component_description", ""),
            "input_schema": component.get("input_schema", {}),
            "output_schema": component.get("output_schema", {}),
            "annotations": None
        }
    
    def group_tools_by_server(self, mcp_tools: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Group MCP tools by server ID.
        
        Args:
            mcp_tools: List of MCP tools in workflow service format
            
        Returns:
            Dictionary mapping server IDs to lists of transformed tools
        """
        grouped_tools = {}
        
        for tool in mcp_tools:
            component = tool.get("component", {})
            mcp_metadata = component.get("mcp_metadata", {})
            server_id = mcp_metadata.get("server_id", "")
            
            if not server_id:
                continue
                
            if server_id not in grouped_tools:
                grouped_tools[server_id] = []
                
            transformed_tool = self.transform_mcp_tool(tool)
            grouped_tools[server_id].append(transformed_tool)
            
        return grouped_tools
    
    def create_mcp_configs(self, grouped_tools: Dict[str, List[Dict[str, Any]]]) -> List[Dict[str, Any]]:
        """
        Create MCP configs from grouped tools.
        
        Args:
            grouped_tools: Dictionary mapping server IDs to lists of transformed tools
            
        Returns:
            List of MCP configs in agent platform format
        """
        mcp_configs = []
        
        for server_id, tools in grouped_tools.items():
            mcp_config = {
                "mcp_tools_config": {
                    "meta": None,
                    "nextCursor": None,
                    "tools": tools
                },
                "is_added": False,
                "env_keys": None,
                "component_category": "social media",  # Default category
                "env_credential_status": None
            }
            mcp_configs.append(mcp_config)
            
        return mcp_configs
    
    def transform_mcp_tools(self, agent_config: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        Transform MCP tools in an agent configuration.
        
        Args:
            agent_config: Agent configuration from workflow service
            
        Returns:
            Tuple of (regular_tools, mcp_configs)
        """
        all_tools = agent_config.get("tools", [])
        
        # Separate MCP tools from regular tools
        mcp_tools = []
        regular_tools = []
        
        for tool in all_tools:
            if (tool.get("tool_type") == "workflow_component" and 
                tool.get("component", {}).get("mcp_metadata")):
                mcp_tools.append(tool)
            else:
                regular_tools.append(tool)
        
        # Group MCP tools by server ID
        grouped_tools = self.group_tools_by_server(mcp_tools)
        
        # Create MCP configs
        mcp_configs = self.create_mcp_configs(grouped_tools)
        
        return regular_tools, mcp_configs
```

### 3.2 Update Orchestration Engine

```python
async def _build_component_agent_request(self, agent_config: dict, query: str, 
                                       execution_type: str, request_id: str, 
                                       tool_parameters: dict) -> dict:
    """
    Build a request for a component agent.
    
    Args:
        agent_config: Agent configuration from workflow service
        query: Query to execute
        execution_type: Execution type (response/interactive)
        request_id: Request ID
        tool_parameters: Tool parameters
        
    Returns:
        Request for agent platform
    """
    # Transform MCP tools
    transformer = MCPToolTransformer()
    regular_tools, mcp_configs = transformer.transform_mcp_tools(agent_config)
    
    return {
        "request_id": request_id,
        "agent_type": "component",
        "execution_type": execution_type,
        "query": query,
        "agent_config": agent_config,
        "tools": regular_tools,  # Only include non-MCP tools
        "mcps": mcp_configs,     # Include transformed MCP configs
        "system_message": agent_config.get("system_message", ""),
        "model_config": agent_config.get("agent_config", {}),
        "correlation_id": self._current_correlation_id,
        "agent_id": agent_id
    }
```

## Phase 4: Verification and Integration

### 4.1 Unit Testing

Run all unit tests to verify the implementation:

```bash
# Run unit tests for MCP tool transformer
python -m pytest tests/test_mcp_tool_transformer.py -v

# Run unit tests for orchestration engine
python -m pytest tests/test_agent_executor.py -v
```

### 4.2 Integration Testing

Create and run integration tests to verify the end-to-end flow:

```bash
# Run integration tests
python -m pytest tests/integration/test_agent_configuration_flow.py -v
```

### 4.3 Manual Verification

Perform manual verification to ensure the implementation works as expected:

1. Create an agent with MCP tools in the workflow builder
2. Execute the agent and verify the configuration is correctly transformed
3. Check the agent platform logs to ensure it receives the correct configuration

## Implementation Timeline

1. **Day 1**: Analysis and verification (Phase 1)
   - Analyze current configuration flow
   - Verify current behavior
   - Create test fixtures

2. **Day 2**: Test creation (Phase 2)
   - Create unit tests for MCP tool transformer
   - Create integration tests for orchestration engine

3. **Day 3**: Implementation (Phase 3)
   - Implement MCP tool transformer
   - Update orchestration engine

4. **Day 4**: Verification and integration (Phase 4)
   - Run unit tests
   - Run integration tests
   - Perform manual verification

5. **Day 5**: Documentation and finalization
   - Update documentation
   - Address any issues found during testing
   - Finalize implementation

## Risks and Mitigations

### Risks

1. **Breaking existing functionality**: The changes might break existing agent execution flows.
   - **Mitigation**: Comprehensive test coverage and backward compatibility checks.

2. **Performance impact**: The transformation logic might impact performance.
   - **Mitigation**: Performance testing and optimization if needed.

3. **Missing edge cases**: There might be edge cases not covered by the implementation.
   - **Mitigation**: Thorough testing with various agent configurations.

## Conclusion

This implementation plan provides a comprehensive approach to addressing the mismatch between how MCP tools are formatted across the RUH AI backend components. By following a TDD approach and focusing on maintaining backward compatibility, we can ensure a smooth transition to the new format without disrupting existing functionality.
